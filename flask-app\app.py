import subprocess
from flask import Flask, request, jsonify
from asgiref.wsgi import WsgiToAsgi
from pydantic import BaseModel
from typing import List

app = Flask(__name__)

# GHunt directory where the GHunt script (main.py) is located
GHUNT_DIR = "GHunt"  # Update with your GHunt folder path

# Pydantic model for request body validation
class EmailRequest(BaseModel):
    email: str

class BatchEmailRequest(BaseModel):
    emails: List[str]

@app.route("/", methods=["GET"])
async def home():
    return jsonify({"message": "Welcome to the flask!"})

@app.route("/ghunt/email", methods=["POST"])
async def ghunt_email():
    email = request.json.get('email')

    print('received email..', email)

    if not email:
        return jsonify({"error": "Email is required!"}), 400

    try:
        print('GHUNT_DIR 1', GHUNT_DIR)

        # Run the GHunt command with the provided email
        result = subprocess.run(
            ['python', 'main.py', 'email', email],
            cwd=GHUNT_DIR,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )

        print('GHUNT_DIR 2', GHUNT_DIR)

        return jsonify({
            "status": 200,
            "stdout": result.stdout,
            "stderr": result.stderr
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/ghunt/emails", methods=["POST"])
async def ghunt_emails():
    emails = request.json.get('emails')

    if not emails:
        return jsonify({"error": "Emails list is required!"}), 400

    try:
        result = subprocess.run(
            ['python', 'main.py', 'batch-email', *emails],
            cwd=GHUNT_DIR,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )

        return jsonify({
            "status": 200,
            "stdout": result.stdout,
            "stderr": result.stderr
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500


# Wrap Flask app with WsgiToAsgi for Uvicorn compatibility
asgi_app = WsgiToAsgi(app)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(asgi_app, host="0.0.0.0", port=5000)
