import asyncio
import json
import sys
from typing import *
from pathlib import Path
import httpx

from ghunt import globals as gb
from ghunt.helpers.utils import get_httpx_client
from ghunt.helpers.multi_account import MultiAccountManager
from ghunt.modules import email, gaia


class BatchMultiAccountProcessor:
    """
    Process multiple targets using multiple Google accounts.
    Automatically rotates between accounts to distribute load.
    """
    
    def __init__(self):
        self.manager = MultiAccountManager()
        self.current_account_index = 0
        self.active_accounts = []
        self.results = {}
        
    async def initialize(self):
        """Initialize and validate all accounts"""
        print("[+] Initializing multi-account processor...")
        
        accounts = self.manager.list_accounts()
        if not accounts:
            print("[-] No accounts found! Please add accounts first.")
            return False
        
        as_client = get_httpx_client()
        
        # Test all accounts
        for account_id, info in accounts.items():
            if info.get('status') == 'active':
                print(f"[+] Testing account: {account_id}")
                creds = await self.manager.get_account_creds(account_id)
                if creds:
                    self.active_accounts.append(account_id)
                    print(f"[+] Account {account_id} is ready")
                else:
                    print(f"[-] Account {account_id} failed validation")
        
        await as_client.aclose()
        
        if not self.active_accounts:
            print("[-] No active accounts available!")
            return False
        
        print(f"[+] {len(self.active_accounts)} accounts ready for processing")
        return True
    
    def get_next_account(self) -> str:
        """Get the next account in rotation"""
        if not self.active_accounts:
            return None
        
        account_id = self.active_accounts[self.current_account_index]
        self.current_account_index = (self.current_account_index + 1) % len(self.active_accounts)
        return account_id
    
    async def process_emails_batch(self, emails: List[str], output_file: str = None, 
                                 delay_between_requests: float = 1.0) -> Dict:
        """
        Process multiple emails using account rotation.
        
        Args:
            emails: List of email addresses to process
            output_file: Optional output file for results
            delay_between_requests: Delay between requests in seconds
        
        Returns:
            Dictionary with results for each email
        """
        if not await self.initialize():
            return {}
        
        print(f"[+] Processing {len(emails)} emails with {len(self.active_accounts)} accounts")
        
        results = {}
        as_client = get_httpx_client()
        
        for i, target_email in enumerate(emails, 1):
            account_id = self.get_next_account()
            print(f"\n[{i}/{len(emails)}] Processing {target_email} with account {account_id}")
            
            try:
                # Import the email module function
                from ghunt.modules.email import hunt as email_hunt
                
                # Process with specific account
                result = await email_hunt(as_client, target_email, account_id=account_id)
                results[target_email] = {
                    "status": "success",
                    "data": result,
                    "processed_by": account_id
                }
                print(f"[+] Successfully processed {target_email}")
                
            except Exception as e:
                print(f"[-] Error processing {target_email}: {e}")
                results[target_email] = {
                    "status": "error",
                    "error": str(e),
                    "processed_by": account_id
                }
            
            # Delay between requests
            if i < len(emails):
                await asyncio.sleep(delay_between_requests)
        
        await as_client.aclose()
        
        # Save results if output file specified
        if output_file:
            self.save_results(results, output_file)
        
        return results
    
    async def process_gaia_ids_batch(self, gaia_ids: List[str], output_file: str = None,
                                   delay_between_requests: float = 1.0) -> Dict:
        """
        Process multiple Gaia IDs using account rotation.
        
        Args:
            gaia_ids: List of Gaia IDs to process
            output_file: Optional output file for results
            delay_between_requests: Delay between requests in seconds
        
        Returns:
            Dictionary with results for each Gaia ID
        """
        if not await self.initialize():
            return {}
        
        print(f"[+] Processing {len(gaia_ids)} Gaia IDs with {len(self.active_accounts)} accounts")
        
        results = {}
        as_client = get_httpx_client()
        
        for i, gaia_id in enumerate(gaia_ids, 1):
            account_id = self.get_next_account()
            print(f"\n[{i}/{len(gaia_ids)}] Processing {gaia_id} with account {account_id}")
            
            try:
                # Import the gaia module function
                from ghunt.modules.gaia import hunt as gaia_hunt
                
                # Process with specific account
                result = await gaia_hunt(as_client, gaia_id, account_id=account_id)
                results[gaia_id] = {
                    "status": "success",
                    "data": result,
                    "processed_by": account_id
                }
                print(f"[+] Successfully processed {gaia_id}")
                
            except Exception as e:
                print(f"[-] Error processing {gaia_id}: {e}")
                results[gaia_id] = {
                    "status": "error",
                    "error": str(e),
                    "processed_by": account_id
                }
            
            # Delay between requests
            if i < len(gaia_ids):
                await asyncio.sleep(delay_between_requests)
        
        await as_client.aclose()
        
        # Save results if output file specified
        if output_file:
            self.save_results(results, output_file)
        
        return results
    
    def save_results(self, results: Dict, output_file: str):
        """Save results to JSON file"""
        try:
            from ghunt.objects.encoders import GHuntEncoder
            
            output_data = {
                "processed_at": asyncio.get_event_loop().time(),
                "total_processed": len(results),
                "accounts_used": self.active_accounts,
                "results": results
            }
            
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(output_data, f, cls=GHuntEncoder, indent=2)
            
            print(f"[+] Results saved to {output_file}")
            
        except Exception as e:
            print(f"[-] Error saving results: {e}")
    
    def print_summary(self, results: Dict):
        """Print processing summary"""
        total = len(results)
        successful = sum(1 for r in results.values() if r.get("status") == "success")
        failed = total - successful
        
        print(f"\n=== Processing Summary ===")
        print(f"Total processed: {total}")
        print(f"Successful: {successful}")
        print(f"Failed: {failed}")
        print(f"Success rate: {(successful/total*100):.1f}%" if total > 0 else "N/A")
        
        # Account usage summary
        account_usage = {}
        for result in results.values():
            account = result.get("processed_by", "unknown")
            account_usage[account] = account_usage.get(account, 0) + 1
        
        print(f"\nAccount usage:")
        for account, count in account_usage.items():
            print(f"  {account}: {count} requests")


async def main():
    """Main entry point for batch multi-account processing"""
    
    if len(sys.argv) < 3:
        print("Usage:")
        print("  python -m ghunt.modules.batch_multi_account emails <email1> <email2> ...")
        print("  python -m ghunt.modules.batch_multi_account emails_file <file.txt>")
        print("  python -m ghunt.modules.batch_multi_account gaia_ids <id1> <id2> ...")
        print("  python -m ghunt.modules.batch_multi_account gaia_file <file.txt>")
        return
    
    command = sys.argv[1].lower()
    processor = BatchMultiAccountProcessor()
    
    if command == "emails":
        emails = sys.argv[2:]
        if not emails:
            print("No emails provided!")
            return
        
        output_file = f"batch_emails_results_{int(asyncio.get_event_loop().time())}.json"
        results = await processor.process_emails_batch(emails, output_file)
        processor.print_summary(results)
    
    elif command == "emails_file":
        file_path = sys.argv[2]
        if not Path(file_path).exists():
            print(f"File not found: {file_path}")
            return
        
        with open(file_path, 'r') as f:
            emails = [line.strip() for line in f if line.strip()]
        
        output_file = f"batch_emails_results_{int(asyncio.get_event_loop().time())}.json"
        results = await processor.process_emails_batch(emails, output_file)
        processor.print_summary(results)
    
    elif command == "gaia_ids":
        gaia_ids = sys.argv[2:]
        if not gaia_ids:
            print("No Gaia IDs provided!")
            return
        
        output_file = f"batch_gaia_results_{int(asyncio.get_event_loop().time())}.json"
        results = await processor.process_gaia_ids_batch(gaia_ids, output_file)
        processor.print_summary(results)
    
    elif command == "gaia_file":
        file_path = sys.argv[2]
        if not Path(file_path).exists():
            print(f"File not found: {file_path}")
            return
        
        with open(file_path, 'r') as f:
            gaia_ids = [line.strip() for line in f if line.strip()]
        
        output_file = f"batch_gaia_results_{int(asyncio.get_event_loop().time())}.json"
        results = await processor.process_gaia_ids_batch(gaia_ids, output_file)
        processor.print_summary(results)
    
    else:
        print(f"Unknown command: {command}")


if __name__ == "__main__":
    asyncio.run(main())
