#!/usr/bin/env python3
"""
Example usage of GHunt Multi-Account Manager

This script demonstrates how to use the multi-account functionality
to process multiple targets with different Google accounts.
"""

import asyncio
import httpx
from pathlib import Path

# Add the GHunt directory to Python path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from ghunt.helpers.multi_account import MultiAccountManager
from ghunt.helpers.utils import get_httpx_client
from ghunt.modules.batch_multi_account import BatchMultiAccountProcessor


async def example_basic_usage():
    """Basic example of multi-account usage"""
    print("=== Basic Multi-Account Usage ===\n")
    
    # Initialize the manager
    manager = MultiAccountManager()
    
    # Add accounts (you would use real tokens here)
    print("Adding accounts...")
    manager.add_account("test1", "<EMAIL>", "User One", 
                       oauth_token="oauth2_4/your_token_here")
    manager.add_account("test2", "<EMAIL>", "User Two", 
                       master_token="aas_et/your_master_token_here")
    
    # List accounts
    print("\nCurrent accounts:")
    manager.print_accounts_status()
    
    # Switch between accounts
    manager.switch_account("test1")
    print(f"Current account: {manager.get_current_account()}")
    
    # Get credentials for specific account
    as_client = get_httpx_client()
    creds = await manager.get_account_creds("test1")
    if creds:
        print(f"Loaded credentials for: {creds.account_email}")
    
    await as_client.aclose()


async def example_batch_processing():
    """Example of batch processing with multiple accounts"""
    print("\n=== Batch Processing Example ===\n")
    
    # Initialize batch processor
    processor = BatchMultiAccountProcessor()
    
    # Example email list
    emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>"
    ]
    
    # Process emails with account rotation
    print("Processing emails with account rotation...")
    results = await processor.process_emails_batch(
        emails, 
        output_file="batch_results.json",
        delay_between_requests=2.0
    )
    
    # Print summary
    processor.print_summary(results)


async def example_account_management():
    """Example of account management operations"""
    print("\n=== Account Management Example ===\n")
    
    manager = MultiAccountManager()
    as_client = get_httpx_client()
    
    # Test all accounts
    accounts = manager.list_accounts()
    for account_id in accounts:
        print(f"Testing account: {account_id}")
        
        # Test authentication
        creds = await manager.get_account_creds(account_id)
        if creds:
            print(f"  ✓ Credentials loaded")
            
            # You could add more tests here like:
            # - Check master token validity
            # - Test API calls
            # - Verify permissions
        else:
            print(f"  ✗ Failed to load credentials")
            
            # Refresh if needed
            print(f"  Attempting to refresh...")
            success = await manager.refresh_account(as_client, account_id)
            if success:
                print(f"  ✓ Refresh successful")
            else:
                print(f"  ✗ Refresh failed")
    
    await as_client.aclose()


def example_configuration_file():
    """Example of setting up accounts from configuration file"""
    print("\n=== Configuration File Setup ===\n")
    
    # This would be used with the interactive setup
    config_example = {
        "accounts": [
            {
                "account_id": "work_account",
                "email": "<EMAIL>",
                "name": "Work Account",
                "oauth_token": "oauth2_4/work_token_here"
            },
            {
                "account_id": "personal_account", 
                "email": "<EMAIL>",
                "name": "Personal Account",
                "master_token": "aas_et/personal_master_token_here"
            }
        ]
    }
    
    print("Example configuration structure:")
    import json
    print(json.dumps(config_example, indent=2))
    
    print("\nTo use this configuration:")
    print("1. Save it to a JSON file")
    print("2. Run: python -m ghunt.modules.multi_login interactive")
    print("3. Choose option 7 (Batch setup from file)")
    print("4. Provide the path to your JSON file")


async def example_single_account_usage():
    """Example of using a specific account for operations"""
    print("\n=== Single Account Usage ===\n")
    
    manager = MultiAccountManager()
    
    # Get specific account
    account_id = "test1"  # Replace with actual account ID
    creds = await manager.get_account_creds(account_id)
    
    if creds:
        print(f"Using account: {creds.account_email}")
        
        # Now you can use this creds object with any GHunt module
        # For example:
        # from ghunt.modules import email
        # result = await email.hunt(as_client, "<EMAIL>", account_id=account_id)
        
        print("Account is ready for GHunt operations!")
    else:
        print("Failed to load account credentials")


def print_usage_instructions():
    """Print detailed usage instructions"""
    print("\n" + "="*60)
    print("GHunt Multi-Account Manager - Usage Instructions")
    print("="*60)
    
    print("\n1. SETUP ACCOUNTS:")
    print("   python -m ghunt.modules.multi_login interactive")
    print("   - Choose option 1 to add accounts")
    print("   - Provide OAuth tokens (oauth2_4/...) or Master tokens (aas_et/...)")
    print("   - No passwords required!")
    
    print("\n2. BATCH PROCESSING:")
    print("   # Process multiple emails")
    print("   python -m ghunt.modules.batch_multi_<NAME_EMAIL> <EMAIL>")
    print("   ")
    print("   # Process from file")
    print("   python -m ghunt.modules.batch_multi_account emails_file emails.txt")
    
    print("\n3. ACCOUNT MANAGEMENT:")
    print("   python -m ghunt.modules.multi_login interactive")
    print("   - Option 2: List accounts")
    print("   - Option 3: Setup/refresh accounts") 
    print("   - Option 5: Switch current account")
    print("   - Option 6: Test authentication")
    
    print("\n4. AUTHENTICATION METHODS:")
    print("   ✓ OAuth tokens (oauth2_4/...)")
    print("   ✓ Master tokens (aas_et/...)")
    print("   ✓ Browser extension (GHunt Companion)")
    print("   ✗ NO passwords needed!")
    
    print("\n5. FEATURES:")
    print("   ✓ Multiple Google accounts")
    print("   ✓ Automatic account rotation")
    print("   ✓ Session management")
    print("   ✓ Batch processing")
    print("   ✓ Error handling & recovery")
    print("   ✓ JSON export/import")


async def main():
    """Main example runner"""
    print("GHunt Multi-Account Manager Examples")
    print("====================================")
    
    # Print usage instructions
    print_usage_instructions()
    
    # Run examples (commented out to avoid errors without real tokens)
    # await example_basic_usage()
    # await example_batch_processing() 
    # await example_account_management()
    # await example_single_account_usage()
    
    example_configuration_file()
    
    print("\n" + "="*60)
    print("Examples completed!")
    print("Replace example tokens with real ones to test functionality.")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main())
