import asyncio
import sys
from typing import *
from pathlib import Path

import httpx

from ghunt import globals as gb
from ghunt.helpers.utils import get_httpx_client
from ghunt.helpers.multi_account import MultiAccountManager
from ghunt.helpers import auth


async def interactive_setup():
    """Interactive setup for multiple accounts"""
    
    print("=== GHunt Multi-Account Manager ===\n")
    
    manager = MultiAccountManager()
    as_client = get_httpx_client()
    
    while True:
        print("\nOptions:")
        print("[1] Add new account")
        print("[2] List accounts")
        print("[3] Setup/refresh account")
        print("[4] Remove account")
        print("[5] Switch current account")
        print("[6] Test account authentication")
        print("[7] Batch setup from file")
        print("[8] Export account list")
        print("[0] Exit")
        
        choice = input("\nChoice => ").strip()
        
        if choice == "1":
            await add_account_interactive(manager)
        
        elif choice == "2":
            manager.print_accounts_status()
        
        elif choice == "3":
            await setup_account_interactive(manager, as_client)
        
        elif choice == "4":
            await remove_account_interactive(manager)
        
        elif choice == "5":
            switch_account_interactive(manager)
        
        elif choice == "6":
            await test_account_interactive(manager, as_client)
        
        elif choice == "7":
            await batch_setup_interactive(manager, as_client)
        
        elif choice == "8":
            export_accounts_interactive(manager)
        
        elif choice == "0":
            print("Goodbye!")
            break
        
        else:
            print("Invalid choice!")
    
    await as_client.aclose()


async def add_account_interactive(manager: MultiAccountManager):
    """Interactive account addition"""
    print("\n=== Add New Account ===")
    
    account_id = input("Account ID (unique identifier): ").strip()
    if not account_id:
        print("Account ID cannot be empty!")
        return
    
    email = input("Email (optional): ").strip()
    name = input("Display name (optional): ").strip()
    
    print("\nAuthentication method:")
    print("[1] OAuth token (oauth2_4/...)")
    print("[2] Master token (aas_et/...)")
    print("[3] Add to registry only (setup later)")
    
    auth_choice = input("Choice => ").strip()
    
    oauth_token = ""
    master_token = ""
    
    if auth_choice == "1":
        oauth_token = input("OAuth token => ").strip()
        if not oauth_token.startswith("oauth2_4/"):
            print("Warning: OAuth token should start with 'oauth2_4/'")
    
    elif auth_choice == "2":
        master_token = input("Master token => ").strip()
        if not master_token.startswith("aas_et/"):
            print("Warning: Master token should start with 'aas_et/'")
    
    elif auth_choice == "3":
        print("Account will be added to registry without authentication.")
    
    else:
        print("Invalid choice!")
        return
    
    success = manager.add_account(account_id, email, name, oauth_token, master_token)
    
    if success and (oauth_token or master_token):
        setup_now = input("Setup authentication now? (Y/n): ").lower()
        if setup_now != "n":
            as_client = get_httpx_client()
            await manager.setup_account(as_client, account_id, oauth_token, master_token)
            await as_client.aclose()


async def setup_account_interactive(manager: MultiAccountManager, as_client: httpx.AsyncClient):
    """Interactive account setup"""
    print("\n=== Setup/Refresh Account ===")
    
    accounts = manager.list_accounts()
    if not accounts:
        print("No accounts found!")
        return
    
    print("Available accounts:")
    for i, (account_id, info) in enumerate(accounts.items(), 1):
        status = info.get('status', 'unknown')
        email = info.get('email', 'Unknown')
        print(f"[{i}] {account_id} ({email}) - Status: {status}")
    
    try:
        choice = int(input("Select account number => ")) - 1
        account_id = list(accounts.keys())[choice]
    except (ValueError, IndexError):
        print("Invalid selection!")
        return
    
    print(f"\nSetting up account: {account_id}")
    
    # Check if we need new tokens
    account_info = accounts[account_id]
    if account_info.get('status') in ['pending_setup', 'error', 'needs_refresh']:
        print("This account needs authentication tokens.")
        
        print("\nAuthentication method:")
        print("[1] OAuth token (oauth2_4/...)")
        print("[2] Master token (aas_et/...)")
        print("[3] Skip token input (refresh existing)")
        
        auth_choice = input("Choice => ").strip()
        
        oauth_token = ""
        master_token = ""
        
        if auth_choice == "1":
            oauth_token = input("OAuth token => ").strip()
        elif auth_choice == "2":
            master_token = input("Master token => ").strip()
        
        await manager.setup_account(as_client, account_id, oauth_token, master_token)
    else:
        # Just refresh
        await manager.refresh_account(as_client, account_id)


async def remove_account_interactive(manager: MultiAccountManager):
    """Interactive account removal"""
    print("\n=== Remove Account ===")
    
    accounts = manager.list_accounts()
    if not accounts:
        print("No accounts found!")
        return
    
    print("Available accounts:")
    for i, (account_id, info) in enumerate(accounts.items(), 1):
        email = info.get('email', 'Unknown')
        print(f"[{i}] {account_id} ({email})")
    
    try:
        choice = int(input("Select account number to remove => ")) - 1
        account_id = list(accounts.keys())[choice]
    except (ValueError, IndexError):
        print("Invalid selection!")
        return
    
    confirm = input(f"Are you sure you want to remove {account_id}? (yes/no): ").lower()
    if confirm == "yes":
        manager.remove_account(account_id)
    else:
        print("Cancelled.")


def switch_account_interactive(manager: MultiAccountManager):
    """Interactive account switching"""
    print("\n=== Switch Account ===")
    
    accounts = manager.list_accounts()
    if not accounts:
        print("No accounts found!")
        return
    
    print("Available accounts:")
    for i, (account_id, info) in enumerate(accounts.items(), 1):
        email = info.get('email', 'Unknown')
        current = " (CURRENT)" if account_id == manager.get_current_account() else ""
        print(f"[{i}] {account_id} ({email}){current}")
    
    try:
        choice = int(input("Select account number => ")) - 1
        account_id = list(accounts.keys())[choice]
    except (ValueError, IndexError):
        print("Invalid selection!")
        return
    
    manager.switch_account(account_id)


async def test_account_interactive(manager: MultiAccountManager, as_client: httpx.AsyncClient):
    """Test account authentication"""
    print("\n=== Test Account Authentication ===")
    
    accounts = manager.list_accounts()
    if not accounts:
        print("No accounts found!")
        return
    
    print("Available accounts:")
    for i, (account_id, info) in enumerate(accounts.items(), 1):
        email = info.get('email', 'Unknown')
        status = info.get('status', 'unknown')
        print(f"[{i}] {account_id} ({email}) - Status: {status}")
    
    try:
        choice = int(input("Select account number => ")) - 1
        account_id = list(accounts.keys())[choice]
    except (ValueError, IndexError):
        print("Invalid selection!")
        return
    
    print(f"\nTesting account: {account_id}")
    
    creds = await manager.get_account_creds(account_id)
    if creds:
        # Test master token
        token_valid = await auth.check_master_token(as_client, creds.android.master_token)
        print(f"Master token valid: {token_valid}")
        
        # Test cookies
        cookies_valid = await auth.check_cookies(as_client, creds.cookies)
        print(f"Cookies valid: {cookies_valid}")
        
        # Test OSIDs
        osids_valid = await auth.check_osids(as_client, creds.cookies, creds.osids)
        print(f"OSIDs valid: {osids_valid}")
        
        if token_valid and cookies_valid and osids_valid:
            print("[+] Account authentication is working properly!")
        else:
            print("[-] Account needs refresh!")
            refresh = input("Refresh now? (Y/n): ").lower()
            if refresh != "n":
                await manager.refresh_account(as_client, account_id)
    else:
        print("[-] Failed to load account credentials!")


async def batch_setup_interactive(manager: MultiAccountManager, as_client: httpx.AsyncClient):
    """Batch setup from JSON file"""
    print("\n=== Batch Setup from File ===")
    
    file_path = input("Enter path to JSON file: ").strip()
    if not Path(file_path).exists():
        print("File not found!")
        return
    
    try:
        import json
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        accounts_data = data.get('accounts', [])
        print(f"Found {len(accounts_data)} accounts in file.")
        
        for account_data in accounts_data:
            account_id = account_data.get('account_id')
            email = account_data.get('email', '')
            name = account_data.get('name', '')
            oauth_token = account_data.get('oauth_token', '')
            master_token = account_data.get('master_token', '')
            
            if not account_id:
                print("Skipping account without ID")
                continue
            
            print(f"\nProcessing account: {account_id}")
            
            # Add to registry
            manager.add_account(account_id, email, name, oauth_token, master_token)
            
            # Setup if tokens provided
            if oauth_token or master_token:
                await manager.setup_account(as_client, account_id, oauth_token, master_token)
        
        print("\n[+] Batch setup completed!")
        
    except Exception as e:
        print(f"Error processing file: {e}")


def export_accounts_interactive(manager: MultiAccountManager):
    """Export accounts list to JSON"""
    print("\n=== Export Accounts ===")
    
    accounts = manager.list_accounts()
    if not accounts:
        print("No accounts to export!")
        return
    
    export_data = {
        "accounts": [],
        "exported_at": manager.accounts.get("last_updated", ""),
        "note": "Tokens are not included for security reasons"
    }
    
    for account_id, info in accounts.items():
        export_data["accounts"].append({
            "account_id": account_id,
            "email": info.get('email', ''),
            "name": info.get('name', ''),
            "status": info.get('status', ''),
            "created_at": info.get('created_at', ''),
            "last_used": info.get('last_used', '')
        })
    
    output_file = input("Output file path (default: accounts_export.json): ").strip()
    if not output_file:
        output_file = "accounts_export.json"
    
    try:
        import json
        with open(output_file, 'w') as f:
            json.dump(export_data, f, indent=2)
        print(f"[+] Accounts exported to {output_file}")
    except Exception as e:
        print(f"Error exporting: {e}")


# Main entry point
async def main():
    """Main entry point for multi-account management"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "interactive":
            await interactive_setup()
        else:
            print("Usage: python -m ghunt.modules.multi_login interactive")
    else:
        await interactive_setup()


if __name__ == "__main__":
    asyncio.run(main())
