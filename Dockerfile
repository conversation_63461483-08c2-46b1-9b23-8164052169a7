# Use an official Python runtime as a parent image
FROM python:3.10

# Set the working directory to /app
WORKDIR /app

# Copy the GHunt folder
COPY GHunt /app/GHunt

# Copy the FastAPI app
COPY flask-app /app/flask-app

# Install any necessary dependencies for GHunt
RUN pip install --upgrade pip
RUN pip install -r /app/GHunt/requirements.txt

# Install any necessary dependencies for the FastAPI app
RUN pip install -r /app/flask-app/requirements.txt

# Expose port 5000 for the FastAPI app
EXPOSE 5000

# Run the FastAPI app using Uvicorn
CMD ["uvicorn", "flask-app.app:asgi_app", "--host", "0.0.0.0", "--port", "5000", "--workers", "4"]
