import subprocess
from flask import Flask, request, jsonify
from asgiref.wsgi import WsgiToAsgi
from pydantic import BaseModel
from typing import List
import json

app = Flask(__name__)

# GHunt directory where the GHunt script (main.py) is located
# GHUNT_DIR = "D:/GHUNT/GHunt"  # Replace with your GHunt folder path
GHUNT_DIR = "GHunt"  # Update with your Docker GHunt folder path

# Pydantic model for request body validation
class EmailRequest(BaseModel):
    email: str

class BatchEmailRequest(BaseModel):
    emails: List[str]

@app.route("/", methods=["GET"])
async def home():
    return jsonify({"message": "Welcome to the flask!"})

@app.route("/ghunt/email", methods=["POST"])
async def ghunt_email():
    email = request.json['email']

    print('received email..', email)

    if not email:
         jsonify({"error": "Email is required!"}), 400

    try:
        print('GHUNT_DIR 1', GHUNT_DIR)

        # Run the GHunt command with the provided email
        result = subprocess.run(
            ['python', 'main.py', 'email', email],
            cwd=GHUNT_DIR,
            capture_output=True,
            text=True,
            encoding='utf-8'  # Add this to ensure UTF-8 encoding
        )

        print('GHUNT_DIR 2', GHUNT_DIR)

        # Capture and parse the output
        output = result.stdout
        parsed_output = parse_output(output, single_email=True)

        return jsonify({"status": 200, "data": parsed_output})
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/ghunt/emails", methods=["POST"])
async def ghunt_emails():
    emails = request.json['emails']

    if not emails:
        return jsonify({"error": "Emails list is required!"}), 400

    try:
        result = subprocess.run(
            ['python', 'main.py', 'batch-email', *emails],
            cwd=GHUNT_DIR,
            capture_output=True,
            text=True,
            encoding='utf-8'  # Ensure UTF-8 encoding
        )

        # Capture and print the output for debugging
        output = result.stdout
        print("GHunt Output:\n", output)

        parsed_output = parse_output(output, single_email=False)
        return jsonify({"status": 200, "data": parsed_output})

    except Exception as e:
        return jsonify({"error": str(e)}), 500



###### Utility functions #######
def parse_output(output: str, single_email: bool):
    try:
        # Try to parse as JSON
        return json.loads(output)
    except json.JSONDecodeError:
        # Fallback to plain text parsing
        if single_email:
            return parse_google_account_data_single_email(output)
        else:
            return parse_delimited_output_for_emails(output)
    

def parse_delimited_output_for_emails(output: str):
    data = {}

    lines = output.strip().split('\n')

    current_email = None

    for line in lines:
        line = line.strip()
        
        if line.startswith("[+] Google Account data for"):
            # Extract the email from the line
            current_email = line.split("for")[-1].strip()
            data[current_email] = {}
        elif current_email and ":" in line:
            # Process key-value pairs
            key, value = line.split(":", 1)
            data[current_email][key.strip()] = value.strip()

    return data


def parse_google_account_data_single_email(text):
    """
    Parse Google account data output into a structured JSON format.
    
    Args:
        text (str): Raw text output containing Google account information
        
    Returns:
        dict: Parsed data in JSON-compatible dictionary format
    """
    # Initialize the result dictionary
    result = {
        "session": {
            "loaded": False,
            "authenticated": False
        },
        "containers": [],
        "profile": {
            "default_profile_picture": False,
            "default_cover_picture": False,
            "last_edit": None,
            "email": None,
            "gaia_id": None,
            "user_types": []
        },
        "google_chat": {
            "entity_type": None,
            "customer_id": None
        },
        "google_plus": {
            "enterprise_user": None
        },
        "play_games": {
            "has_player_profile": False
        }
    }
    
    # Split the text into lines and remove empty lines
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    
    # Parse each line
    for i, line in enumerate(lines):
        # Session info
        if '[+] Stored session loaded' in line:
            result["session"]["loaded"] = True
        elif '[+] Authenticated' in line:
            result["session"]["authenticated"] = True
            
        # Containers
        elif line.startswith('- '):
            if i > 0 and lines[i-1].endswith('containers :'):
                result["containers"].append(line[2:])
                
        # Profile info
        elif '[-] Default profile picture' in line:
            result["profile"]["default_profile_picture"] = True
        elif '[-] Default cover picture' in line:
            result["profile"]["default_cover_picture"] = True
        elif line.startswith('Last profile edit :'):
            try:
                date_str = line.split(' : ')[1].split(' (UTC)')[0]
                result["profile"]["last_edit"] = date_str
            except:
                pass
            
        # Email and Gaia ID
        elif line.startswith('Email :'):
            result["profile"]["email"] = line.split(' : ')[1]
        elif line.startswith('Gaia ID :'):
            result["profile"]["gaia_id"] = line.split(' : ')[1]
            
        # User types
        elif line.startswith('- GOOGLE_USER'):
            result["profile"]["user_types"].append({
                "type": "GOOGLE_USER",
                "description": line.split('(')[1].rstrip(')')
            })
            
        # Google Chat data
        elif line.startswith('Entity Type :'):
            result["google_chat"]["entity_type"] = line.split(' : ')[1]
        elif line.startswith('Customer ID :'):
            result["google_chat"]["customer_id"] = None if 'Not found' in line else line.split(' : ')[1]
            
        # Google Plus data
        elif line.startswith('Entreprise User :'):
            result["google_plus"]["enterprise_user"] = line.split(' : ')[1].lower() == 'true'
            
        # Play Games data
        elif '[-] No player profile found' in line:
            result["play_games"]["has_player_profile"] = False
    
    return result


# Wrap Flask app with WsgiToAsgi for Uvicorn compatibility
asgi_app = WsgiToAsgi(app)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(asgi_app, host="0.0.0.0", port=5000)