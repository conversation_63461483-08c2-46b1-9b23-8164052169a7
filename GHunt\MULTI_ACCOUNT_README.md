# GHunt Multi-Account Manager

This enhanced version of GHunt supports managing multiple Google accounts for OSINT operations. It automatically handles authentication, session management, and account rotation.

## 🔑 Authentication Methods

**NO PASSWORDS REQUIRED!** The system uses OAuth-based authentication:

- **OAuth Tokens** (starts with `oauth2_4/...`)
- **Master Tokens** (starts with `aas_et/...`) 
- **Browser Extension** (GHunt Companion)

## 🚀 Quick Start

### 1. Setup Accounts

```bash
# Interactive setup
python -m ghunt.modules.multi_login interactive

# Choose option 1 to add accounts
# Provide either OAuth tokens or Master tokens
```

### 2. Batch Processing

```bash
# Process multiple emails with account rotation
python -m ghunt.modules.batch_multi_<NAME_EMAIL> <EMAIL>

# Process from file
python -m ghunt.modules.batch_multi_account emails_file targets.txt

# Process Gaia IDs
python -m ghunt.modules.batch_multi_account gaia_ids ********* *********
```

### 3. Account Management

```bash
# Interactive management
python -m ghunt.modules.multi_login interactive

# Options available:
# [2] List accounts
# [3] Setup/refresh account  
# [5] Switch current account
# [6] Test authentication
```

## 📁 File Structure

```
~/.malfrats/ghunt/
├── accounts.json          # Account registry
├── creds_account1.m       # Account 1 credentials
├── creds_account2.m       # Account 2 credentials
└── creds_default.m        # Default account (legacy)
```

## 🔧 Configuration File Setup

Create a JSON file with your accounts:

```json
{
  "accounts": [
    {
      "account_id": "work_account",
      "email": "<EMAIL>", 
      "name": "Work Account",
      "oauth_token": "oauth2_4/your_oauth_token_here"
    },
    {
      "account_id": "personal_account",
      "email": "<EMAIL>",
      "name": "Personal Account", 
      "master_token": "aas_et/your_master_token_here"
    }
  ]
}
```

Then use batch setup:
```bash
python -m ghunt.modules.multi_login interactive
# Choose option 7: Batch setup from file
```

## 💻 Programmatic Usage

```python
import asyncio
from ghunt.helpers.multi_account import MultiAccountManager
from ghunt.helpers.utils import get_httpx_client

async def example():
    # Initialize manager
    manager = MultiAccountManager()
    
    # Add account
    manager.add_account("test1", "<EMAIL>", "User", 
                       oauth_token="oauth2_4/token_here")
    
    # Setup authentication
    as_client = get_httpx_client()
    await manager.setup_account(as_client, "test1", 
                               oauth_token="oauth2_4/token_here")
    
    # Get credentials for use
    creds = await manager.get_account_creds("test1")
    
    # Use with GHunt modules
    from ghunt.modules import email
    result = await email.hunt(as_client, "<EMAIL>", 
                             account_id="test1")
    
    await as_client.aclose()

asyncio.run(example())
```

## 🔄 Account Rotation

The batch processor automatically rotates between accounts:

```python
from ghunt.modules.batch_multi_account import BatchMultiAccountProcessor

async def batch_example():
    processor = BatchMultiAccountProcessor()
    
    emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
    
    # Automatically rotates between available accounts
    results = await processor.process_emails_batch(
        emails,
        output_file="results.json",
        delay_between_requests=2.0
    )
    
    processor.print_summary(results)
```

## 🛡️ Security Features

- **Encrypted Storage**: All credentials are base64-encoded
- **No Password Storage**: Only OAuth/Master tokens are stored
- **Account Isolation**: Each account has separate credential files
- **Session Validation**: Automatic token validation and refresh
- **Error Recovery**: Automatic retry with different accounts

## 📊 Account Status Monitoring

```bash
# Check account status
python -m ghunt.modules.multi_login interactive
# Choose option 2: List accounts

# Test specific account
# Choose option 6: Test account authentication
```

Account statuses:
- `active`: Ready for use
- `pending_setup`: Needs authentication setup
- `needs_refresh`: Requires token refresh
- `error`: Authentication failed

## 🔍 Advanced Features

### Custom Account Switching

```python
# Switch to specific account
manager.switch_account("work_account")

# Get current account
current = manager.get_current_account()

# Use specific account for operation
creds = await manager.get_account_creds("personal_account")
```

### Batch Processing with Custom Logic

```python
async def custom_batch_processing():
    processor = BatchMultiAccountProcessor()
    
    # Custom processing logic
    for email in email_list:
        account_id = processor.get_next_account()
        
        # Custom processing per account
        if account_id == "work_account":
            # Use work account for work-related targets
            pass
        elif account_id == "personal_account":
            # Use personal account for personal targets
            pass
        
        # Process with selected account
        result = await process_target(email, account_id)
```

### Export/Import Accounts

```bash
# Export account list (without tokens for security)
python -m ghunt.modules.multi_login interactive
# Choose option 8: Export account list

# Import from configuration file
# Choose option 7: Batch setup from file
```

## 🚨 Important Notes

1. **No Passwords**: Never provide Google account passwords
2. **Token Security**: Keep OAuth/Master tokens secure
3. **Rate Limiting**: Use delays between requests to avoid rate limits
4. **Account Limits**: Google may limit API usage per account
5. **Terms of Service**: Ensure compliance with Google's ToS

## 🐛 Troubleshooting

### Account Authentication Failed
```bash
# Refresh account
python -m ghunt.modules.multi_login interactive
# Choose option 3: Setup/refresh account
```

### Invalid Tokens
- OAuth tokens expire quickly (hours)
- Master tokens last longer (weeks/months)
- Use GHunt Companion extension for fresh tokens

### Session Issues
```bash
# Test account authentication
python -m ghunt.modules.multi_login interactive  
# Choose option 6: Test account authentication
```

### No Active Accounts
```bash
# Add new accounts
python -m ghunt.modules.multi_login interactive
# Choose option 1: Add new account
```

## 📝 Example Files

- `examples/multi_account_setup.json` - Configuration template
- `examples/multi_account_usage.py` - Usage examples
- `examples/targets.txt` - Sample target list

## 🤝 Contributing

To extend the multi-account functionality:

1. Modify `ghunt/helpers/multi_account.py` for core features
2. Update `ghunt/modules/multi_login.py` for CLI interface
3. Extend `ghunt/modules/batch_multi_account.py` for batch processing
4. Add tests and documentation

## 📄 License

Same as GHunt - AGPL-3.0 License
