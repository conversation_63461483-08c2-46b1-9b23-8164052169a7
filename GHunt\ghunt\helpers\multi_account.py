import asyncio
import json
import base64
from typing import *
from pathlib import Path
from datetime import datetime, timedelta
import httpx

from ghunt import globals as gb
from ghunt.objects.base import GHuntCreds
from ghunt.errors import *
from ghunt.helpers.utils import *
from ghunt.helpers import auth
from ghunt.helpers.knowledge import get_domain_of_service
from ghunt.knowledge.services import services_baseurls


class MultiAccountManager:
    """
    Manages multiple Google accounts for GHunt operations.
    Handles authentication, token management, and account switching.
    """
    
    def __init__(self, base_path: str = ""):
        if not base_path:
            self.base_path = Path().home() / ".malfrats/ghunt"
        else:
            self.base_path = Path(base_path)
        
        self.base_path.mkdir(parents=True, exist_ok=True)
        self.accounts_file = self.base_path / "accounts.json"
        self.accounts: Dict[str, Dict] = {}
        self.current_account: str = ""
        self.load_accounts_registry()
    
    def load_accounts_registry(self):
        """Load the accounts registry file"""
        if self.accounts_file.exists():
            try:
                with open(self.accounts_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.accounts = data.get("accounts", {})
                    self.current_account = data.get("current_account", "")
            except Exception as e:
                print(f"[!] Error loading accounts registry: {e}")
                self.accounts = {}
    
    def save_accounts_registry(self):
        """Save the accounts registry file"""
        data = {
            "accounts": self.accounts,
            "current_account": self.current_account,
            "last_updated": datetime.now().isoformat()
        }
        
        with open(self.accounts_file, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=2)
    
    def add_account(self, account_id: str, email: str = "", name: str = "", 
                   oauth_token: str = "", master_token: str = "") -> bool:
        """
        Add a new account to the manager.
        
        Args:
            account_id: Unique identifier for the account
            email: Account email address
            name: Account display name
            oauth_token: OAuth token (starts with oauth2_4/)
            master_token: Master token (starts with aas_et/)
        
        Returns:
            bool: True if account was added successfully
        """
        if account_id in self.accounts:
            print(f"[!] Account {account_id} already exists!")
            return False
        
        if not oauth_token and not master_token:
            print("[!] Either oauth_token or master_token must be provided!")
            return False
        
        self.accounts[account_id] = {
            "email": email,
            "name": name,
            "created_at": datetime.now().isoformat(),
            "last_used": "",
            "status": "pending_setup",
            "has_oauth_token": bool(oauth_token),
            "has_master_token": bool(master_token)
        }
        
        self.save_accounts_registry()
        print(f"[+] Account {account_id} added to registry!")
        return True
    
    def list_accounts(self) -> Dict[str, Dict]:
        """List all registered accounts"""
        return self.accounts.copy()
    
    def remove_account(self, account_id: str) -> bool:
        """Remove an account and its credentials"""
        if account_id not in self.accounts:
            print(f"[!] Account {account_id} not found!")
            return False
        
        # Remove credentials file
        creds_path = self.base_path / f"creds_{account_id}.m"
        if creds_path.exists():
            creds_path.unlink()
            print(f"[+] Credentials file for {account_id} deleted!")
        
        # Remove from registry
        del self.accounts[account_id]
        
        # Update current account if needed
        if self.current_account == account_id:
            self.current_account = ""
        
        self.save_accounts_registry()
        print(f"[+] Account {account_id} removed!")
        return True
    
    async def setup_account(self, as_client: httpx.AsyncClient, account_id: str, 
                          oauth_token: str = "", master_token: str = "") -> bool:
        """
        Setup authentication for a specific account.
        
        Args:
            as_client: HTTP client
            account_id: Account identifier
            oauth_token: OAuth token if available
            master_token: Master token if available
        
        Returns:
            bool: True if setup was successful
        """
        if account_id not in self.accounts:
            print(f"[!] Account {account_id} not found in registry!")
            return False
        
        try:
            # Create GHuntCreds for this account
            ghunt_creds = GHuntCreds(account_id=account_id)
            ghunt_creds.account_id = account_id
            
            # Reset authorization tokens
            ghunt_creds.android.authorization_tokens = {}
            
            if oauth_token:
                print(f"[+] Setting up account {account_id} with OAuth token...")
                master_token, services, owner_email, owner_name = await auth.android_master_auth(as_client, oauth_token)
                
                # Update account info
                ghunt_creds.account_email = owner_email
                ghunt_creds.account_name = owner_name
                
                self.accounts[account_id]["email"] = owner_email
                self.accounts[account_id]["name"] = owner_name
                
                print(f"[+] Account: {owner_name} ({owner_email})")
                print(f"[+] Master token services: {', '.join(services)}")
            
            elif master_token:
                print(f"[+] Setting up account {account_id} with master token...")
                # Validate master token
                if not await auth.check_master_token(as_client, master_token):
                    print(f"[-] Invalid master token for account {account_id}!")
                    return False
            
            # Set the master token
            ghunt_creds.android.master_token = master_token
            
            # Generate cookies and OSIDs
            print(f"[+] Generating cookies and OSIDs for {account_id}...")
            ghunt_creds.cookies = {"temp": "temp"}  # Dummy data
            ghunt_creds.osids = {"temp": "temp"}    # Dummy data
            
            await auth.gen_cookies_and_osids(as_client, ghunt_creds)
            print(f"[+] Cookies and OSIDs generated for {account_id}!")
            
            # Save credentials
            ghunt_creds.save_creds(silent=True)
            
            # Update account status
            self.accounts[account_id]["status"] = "active"
            self.accounts[account_id]["last_used"] = datetime.now().isoformat()
            self.save_accounts_registry()
            
            print(f"[+] Account {account_id} setup completed successfully!")
            return True
            
        except Exception as e:
            print(f"[-] Error setting up account {account_id}: {e}")
            self.accounts[account_id]["status"] = "error"
            self.save_accounts_registry()
            return False
    
    async def get_account_creds(self, account_id: str) -> Optional[GHuntCreds]:
        """
        Get authenticated credentials for a specific account.
        
        Args:
            account_id: Account identifier
        
        Returns:
            GHuntCreds object if successful, None otherwise
        """
        if account_id not in self.accounts:
            print(f"[!] Account {account_id} not found!")
            return None
        
        try:
            ghunt_creds = GHuntCreds(account_id=account_id)
            ghunt_creds.load_creds(silent=True)
            
            # Update last used
            self.accounts[account_id]["last_used"] = datetime.now().isoformat()
            self.current_account = account_id
            self.save_accounts_registry()
            
            return ghunt_creds
            
        except GHuntInvalidSession as e:
            print(f"[-] Invalid session for account {account_id}: {e}")
            self.accounts[account_id]["status"] = "needs_refresh"
            self.save_accounts_registry()
            return None
    
    async def refresh_account(self, as_client: httpx.AsyncClient, account_id: str) -> bool:
        """
        Refresh authentication for a specific account.
        
        Args:
            as_client: HTTP client
            account_id: Account identifier
        
        Returns:
            bool: True if refresh was successful
        """
        if account_id not in self.accounts:
            print(f"[!] Account {account_id} not found!")
            return False
        
        try:
            ghunt_creds = GHuntCreds(account_id=account_id)
            ghunt_creds.load_creds(silent=True)
            
            # Check and regenerate if needed
            await auth.check_and_gen(as_client, ghunt_creds)
            
            # Update status
            self.accounts[account_id]["status"] = "active"
            self.accounts[account_id]["last_used"] = datetime.now().isoformat()
            self.save_accounts_registry()
            
            print(f"[+] Account {account_id} refreshed successfully!")
            return True
            
        except Exception as e:
            print(f"[-] Error refreshing account {account_id}: {e}")
            self.accounts[account_id]["status"] = "error"
            self.save_accounts_registry()
            return False
    
    def switch_account(self, account_id: str) -> bool:
        """Switch to a different account"""
        if account_id not in self.accounts:
            print(f"[!] Account {account_id} not found!")
            return False
        
        self.current_account = account_id
        self.save_accounts_registry()
        print(f"[+] Switched to account {account_id}")
        return True
    
    def get_current_account(self) -> str:
        """Get the currently active account ID"""
        return self.current_account
    
    def print_accounts_status(self):
        """Print status of all accounts"""
        if not self.accounts:
            print("[!] No accounts registered!")
            return
        
        print("\n=== Account Status ===")
        for account_id, info in self.accounts.items():
            current_marker = " (CURRENT)" if account_id == self.current_account else ""
            print(f"\nAccount ID: {account_id}{current_marker}")
            print(f"  Email: {info.get('email', 'Unknown')}")
            print(f"  Name: {info.get('name', 'Unknown')}")
            print(f"  Status: {info.get('status', 'Unknown')}")
            print(f"  Created: {info.get('created_at', 'Unknown')}")
            print(f"  Last Used: {info.get('last_used', 'Never')}")
